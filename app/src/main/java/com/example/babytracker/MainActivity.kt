package com.example.babytracker

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.babytracker.data.AppDatabase
import com.example.babytracker.data.BabyEvent
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date


class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val db = AppDatabase.getDatabase(this)

        setContent {
            MaterialTheme {
                val scope = rememberCoroutineScope()
                var events by remember { mutableStateOf(listOf<BabyEvent>()) }

                LaunchedEffect(Unit) {
                    scope.launch {
                        events = db.eventDao().getAll()
                    }
                }

                Column(Modifier.padding(16.dp)) {
                    Text("Recent Activities", style = MaterialTheme.typography.titleLarge)
                    Spacer(Modifier.height(8.dp))
                    events.forEach {
                        Text("${it.type.label} at ${SimpleDateFormat("hh:mm a").format(Date(it.timestamp))}")
                    }
                }
            }
        }
    }
}