package com.example.babytracker

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.example.babytracker.data.AppDatabase
import com.example.babytracker.data.BabyEvent
import com.example.babytracker.data.EventType
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlinx.coroutines.launch

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val db = AppDatabase.getDatabase(this)

        setContent {
            MaterialTheme {
                val scope = rememberCoroutineScope()
                var events by remember { mutableStateOf(listOf<BabyEvent>()) }

                // Function to load events
                val loadEvents = { scope.launch { events = db.eventDao().getAll() } }

                // Load events initially
                LaunchedEffect(Unit) { loadEvents() }

                // Refresh events when activity resumes
                val lifecycleOwner = LocalLifecycleOwner.current
                DisposableEffect(lifecycleOwner) {
                    val observer = LifecycleEventObserver { _, event ->
                        if (event == Lifecycle.Event.ON_RESUME) {
                            loadEvents()
                        }
                    }
                    lifecycleOwner.lifecycle.addObserver(observer)
                    onDispose { lifecycleOwner.lifecycle.removeObserver(observer) }
                }

                Column(Modifier.padding(16.dp)) {
                    Text("Recent Activities", style = MaterialTheme.typography.titleLarge)
                    Spacer(Modifier.height(8.dp))

                    // Test button to add an event manually
                    Button(
                            onClick = {
                                scope.launch {
                                    val testEvent = BabyEvent(type = EventType.FEED_START)
                                    db.eventDao().insert(testEvent)
                                    loadEvents() // Refresh the list
                                }
                            }
                    ) { Text("Test: Add Feed Start Event") }

                    Spacer(Modifier.height(16.dp))

                    if (events.isEmpty()) {
                        Text("No events yet. Use the widget or test button to log activities!")
                    } else {
                        events.forEach { event ->
                            Text(
                                    "${event.type.label} at ${SimpleDateFormat("hh:mm a", Locale.getDefault()).format(Date(event.timestamp))}"
                            )
                        }
                    }
                }
            }
        }
    }
}
