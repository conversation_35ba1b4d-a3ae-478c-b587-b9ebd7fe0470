package com.example.babytracker

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.example.babytracker.data.AppDatabase
import com.example.babytracker.data.BabyEvent
import com.example.babytracker.data.EventType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BabyWidgetReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
        val typeName = intent.getStringExtra("EVENT_TYPE") ?: return
        val type = EventType.valueOf(typeName)
        CoroutineScope(Dispatchers.IO).launch {
            val db = AppDatabase.getDatabase(context)
            db.eventDao().insert(BabyEvent(type = type))
        }
    }
}