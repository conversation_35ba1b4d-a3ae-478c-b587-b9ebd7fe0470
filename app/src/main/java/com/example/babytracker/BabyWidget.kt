package com.example.babytracker

import android.content.Context
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.glance.Button
import androidx.glance.ButtonDefaults
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.action.ActionParameters
import androidx.glance.action.actionParametersOf
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.action.ActionCallback
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.provideContent
import androidx.glance.background
import androidx.glance.layout.Alignment
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.Spacer
import androidx.glance.layout.fillMaxSize
import androidx.glance.layout.fillMaxWidth
import androidx.glance.layout.height
import androidx.glance.layout.padding
import androidx.glance.layout.width
import androidx.glance.text.Text
import androidx.glance.text.TextStyle
import androidx.glance.unit.ColorProvider
import com.example.babytracker.data.AppDatabase
import com.example.babytracker.data.BabyEvent
import com.example.babytracker.data.EventType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BabyWidget : GlanceAppWidget() {
    override suspend fun provideGlance(context: Context, id: GlanceId) {
        provideContent {
            Column(
                    modifier =
                            GlanceModifier.fillMaxSize()
                                    .background(
                                            ColorProvider(
                                                    android.graphics.Color.parseColor("#F5F5F5")
                                            )
                                    )
                                    .padding(12.dp),
                    verticalAlignment = Alignment.Top,
                    horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Title
                Text(
                        text = "Baby Tracker",
                        style =
                                TextStyle(
                                        fontSize = 16.sp,
                                        color =
                                                ColorProvider(
                                                        android.graphics.Color.parseColor("#333333")
                                                )
                                ),
                        modifier = GlanceModifier.padding(bottom = 8.dp)
                )

                Spacer(modifier = GlanceModifier.height(4.dp))

                // Buttons in a 2x3 grid
                EventType.entries.chunked(2).forEach { row ->
                    Row(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = GlanceModifier.fillMaxWidth()
                    ) {
                        row.forEach { type ->
                            Button(
                                    text = type.label,
                                    onClick =
                                            actionRunCallback<LogEventAction>(
                                                    actionParametersOf(
                                                            ActionParameters.Key<String>(
                                                                    "EVENT_TYPE"
                                                            ) to type.name
                                                    )
                                            ),
                                    modifier =
                                            GlanceModifier.padding(
                                                            horizontal = 4.dp,
                                                            vertical = 2.dp
                                                    )
                                                    .width(80.dp),
                                    colors =
                                            ButtonDefaults.buttonColors(
                                                    backgroundColor =
                                                            ColorProvider(
                                                                    android.graphics.Color
                                                                            .parseColor("#4CAF50")
                                                            ),
                                                    contentColor =
                                                            ColorProvider(
                                                                    android.graphics.Color.WHITE
                                                            )
                                            )
                            )
                        }
                        // Add spacer if row has only one item to center it
                        if (row.size == 1) {
                            Spacer(modifier = GlanceModifier.width(88.dp))
                        }
                    }
                    Spacer(modifier = GlanceModifier.height(4.dp))
                }
            }
        }
    }
}

class LogEventAction : ActionCallback {
    override suspend fun onAction(
            context: Context,
            glanceId: GlanceId,
            parameters: ActionParameters
    ) {
        val typeName = parameters[ActionParameters.Key<String>("EVENT_TYPE")] ?: return
        val type = EventType.valueOf(typeName)
        CoroutineScope(Dispatchers.IO).launch {
            val db = AppDatabase.getDatabase(context)
            db.eventDao().insert(BabyEvent(type = type))
        }
    }
}
