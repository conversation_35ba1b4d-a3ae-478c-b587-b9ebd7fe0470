package com.example.babytracker

import android.content.Context
import androidx.compose.ui.unit.dp
import androidx.glance.Button
import androidx.glance.GlanceId
import androidx.glance.GlanceModifier
import androidx.glance.action.ActionParameters
import androidx.glance.action.actionParametersOf
import androidx.glance.appwidget.GlanceAppWidget
import androidx.glance.appwidget.action.ActionCallback
import androidx.glance.appwidget.action.actionRunCallback
import androidx.glance.appwidget.provideContent
import androidx.glance.layout.Alignment
import androidx.glance.layout.Column
import androidx.glance.layout.Row
import androidx.glance.layout.padding
import com.example.babytracker.data.AppDatabase
import com.example.babytracker.data.BabyEvent
import com.example.babytracker.data.EventType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class BabyWidget : GlanceAppWidget() {
    override suspend fun provideGlance(context: Context, id: GlanceId) {
        provideContent {
            Column(
                    modifier = GlanceModifier.padding(8.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalAlignment = Alignment.CenterHorizontally
            ) {
                EventType.entries.chunked(3).forEach { row ->
                    Row(horizontalAlignment = Alignment.CenterHorizontally) {
                        row.forEach { type ->
                            Button(
                                    text = type.label,
                                    onClick =
                                            actionRunCallback<LogEventAction>(
                                                    actionParametersOf(
                                                            ActionParameters.Key<String>(
                                                                    "EVENT_TYPE"
                                                            ) to type.name
                                                    )
                                            ),
                                    modifier = GlanceModifier.padding(4.dp)
                            )
                        }
                    }
                }
            }
        }
    }
}

class LogEventAction : ActionCallback {
    override suspend fun onAction(
            context: Context,
            glanceId: GlanceId,
            parameters: ActionParameters
    ) {
        val typeName = parameters[ActionParameters.Key<String>("EVENT_TYPE")] ?: return
        val type = EventType.valueOf(typeName)
        CoroutineScope(Dispatchers.IO).launch {
            val db = AppDatabase.getDatabase(context)
            db.eventDao().insert(BabyEvent(type = type))
        }
    }
}
